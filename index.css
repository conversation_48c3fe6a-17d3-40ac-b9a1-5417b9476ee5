@import "tailwindcss";
@config "./tailwind.config.js";

/* Athena Blog - Custom Styles */

/* Color scheme support for proper dark mode */
:root {
  color-scheme: light;
}

.dark {
  color-scheme: dark;
}

/* Ensure full page dark mode coverage */
html {
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

html.dark {
  background-color: #1a202c;
}

body {
  background-color: inherit;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  @apply bg-accent rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-accent-dark;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar {
  width: 6px;
}

.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
  @apply bg-accent rounded;
}

.dark ::-webkit-scrollbar-thumb:hover {
  @apply bg-accent-dark;
}

/* Typography enhancements */
.font-serif {
  font-feature-settings: "liga" 1, "kern" 1;
}

/* Image hover effects */
.image-hover-effect {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-hover-effect:hover {
  transform: scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced Focus styles for mobile accessibility */
.focus-visible:focus {
  @apply outline-accent outline-2 outline-offset-2;
}

/* Mobile-specific focus styles */
@media (max-width: 768px) {
  button:focus,
  a:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 3px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 4px;
  }
  
  /* Enhanced focus for interactive elements */
  .mobile-focus:focus {
    outline: 3px solid #3b82f6;
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.2);
  }
}

/* Selection color */
::selection {
  @apply bg-accent text-white;
}

/* Gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-accent to-accent-dark text-transparent bg-clip-text;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dark mode card hover */
.dark .card-hover:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-md {
    background-color: rgba(255, 255, 255, 0.95);
  }
  
  .dark .backdrop-blur-md {
    background-color: rgba(26, 26, 26, 0.95);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Mobile-specific enhancements */
@media (max-width: 768px) {
  /* Improved mobile scrolling */
  html {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
  
  /* Mobile-optimized containers */
  .mobile-container {
    padding-left: 16px;
    padding-right: 16px;
    max-width: 100%;
  }
  
  /* Enhanced mobile card interactions */
  .mobile-card {
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .mobile-card:active {
    transform: scale(0.98);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  /* Mobile navigation improvements */
  .mobile-nav {
    position: sticky;
    top: 0;
    z-index: 50;
    backdrop-filter: blur(12px);
    background-color: rgba(255, 255, 255, 0.95);
  }
  
  .dark .mobile-nav {
    background-color: rgba(26, 32, 44, 0.95);
  }
  
  /* Mobile menu button */
  .mobile-menu-button {
    padding: 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
  }
  
  .mobile-menu-button:active {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(0.95);
  }
  
  /* Mobile dropdown menu */
  .mobile-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 60;
  }
  
  .dark .mobile-dropdown {
    background-color: #1a202c;
  }
  
  /* Mobile text selection */
  .mobile-selectable {
    -webkit-user-select: text;
    user-select: text;
    -webkit-touch-callout: default;
  }
  
  /* Mobile image optimization */
  .mobile-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    object-fit: cover;
  }
  
  /* Mobile spacing utilities */
  .mobile-spacing-sm {
    margin: 8px 0;
  }
  
  .mobile-spacing-md {
    margin: 16px 0;
  }
  
  .mobile-spacing-lg {
    margin: 24px 0;
  }
  
  /* Mobile grid improvements */
  .mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 16px;
  }
  
  /* Mobile accessibility improvements */
  .mobile-accessible {
    font-size: 16px;
    line-height: 1.5;
    color: #1f2937;
    font-weight: 400;
  }
  
  .dark .mobile-accessible {
    color: #f9fafb;
  }
  
  /* Mobile loading states */
  .mobile-loading {
    padding: 40px 20px;
    text-align: center;
  }
  
  .mobile-spinner {
    width: 32px;
    height: 32px;
    margin: 0 auto 16px;
  }
}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:vertical-text {
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }
}

/* Mobile-first responsive text sizing and touch targets */
@media (max-width: 639px) {
  .mobile-text-adjust {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

/* Mobile touch target optimization */
@media (max-width: 768px) {
  /* Minimum 44px touch targets */
  button,
  a,
  input[type="button"],
  input[type="submit"],
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
  }
  
  /* Enhanced spacing for mobile navigation */
  nav a,
  nav button {
    padding: 16px 20px;
    margin: 4px 0;
  }
  
  /* Mobile card spacing */
  .mobile-card-spacing {
    margin-bottom: 24px;
    padding: 20px;
  }
  
  /* Mobile typography improvements */
  h1 {
    font-size: 2rem;
    line-height: 1.2;
    margin-bottom: 1rem;
  }
  
  h2 {
    font-size: 1.75rem;
    line-height: 1.3;
    margin-bottom: 0.875rem;
  }
  
  h3 {
    font-size: 1.5rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
  }
  
  p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
  }
  
  /* Improved text contrast for mobile */
  .mobile-text-contrast {
    color: #1f2937;
    font-weight: 500;
  }
  
  .dark .mobile-text-contrast {
    color: #f9fafb;
  }
  
  /* Mobile button improvements */
  .mobile-button {
    font-size: 1rem;
    font-weight: 600;
    padding: 14px 24px;
    border-radius: 8px;
    transition: all 0.2s ease;
  }
  
  .mobile-button:active {
    transform: scale(0.98);
  }
  
  /* Mobile link improvements */
  .mobile-link {
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 4px;
    padding: 8px 4px;
  }
  
  /* Mobile form elements */
  input,
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 14px 16px;
    border-radius: 8px;
    border: 2px solid #d1d5db;
  }
  
  input:focus,
   textarea:focus,
   select:focus {
     border-color: #3b82f6;
     box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
   }
 }

/* Extra small mobile devices (phones in portrait) */
@media (max-width: 480px) {
  /* Enhanced typography for very small screens */
  body {
    font-size: 14px;
    line-height: 1.6;
  }
  
  h1 {
    font-size: 1.75rem;
    line-height: 1.2;
  }
  
  h2 {
    font-size: 1.5rem;
    line-height: 1.3;
  }
  
  h3 {
    font-size: 1.25rem;
    line-height: 1.4;
  }
  
  /* Larger touch targets for small screens */
  button,
  a,
  .touch-target {
    min-height: 48px;
    min-width: 48px;
    padding: 16px 20px;
  }
  
  /* Enhanced spacing for readability */
  .small-mobile-spacing {
    padding: 16px 12px;
    margin: 12px 0;
  }
  
  /* Improved card layout for small screens */
  .small-mobile-card {
    padding: 16px;
    margin: 12px 8px;
    border-radius: 8px;
  }
  
  /* Better text contrast for small screens */
  .small-mobile-text {
    font-size: 15px;
    line-height: 1.7;
    color: #111827;
    font-weight: 500;
  }
  
  .dark .small-mobile-text {
    color: #f3f4f6;
  }
}

/* Mobile landscape orientation */
@media (max-width: 768px) and (orientation: landscape) {
  /* Optimize for landscape mobile viewing */
  .landscape-mobile {
    padding: 12px 20px;
  }
  
  /* Adjust navigation for landscape */
  .landscape-nav {
    height: 60px;
    padding: 8px 16px;
  }
  
  /* Compact spacing for landscape */
  .landscape-spacing {
    margin: 8px 0;
    padding: 8px 12px;
  }
}

/* High contrast mode support for accessibility */
@media (prefers-contrast: high) {
  button,
  a,
  input {
    border: 2px solid currentColor;
  }
  
  .high-contrast-focus:focus {
    outline: 4px solid #000;
    outline-offset: 2px;
    background-color: #ffff00;
    color: #000;
  }
  
  .dark .high-contrast-focus:focus {
    outline: 4px solid #fff;
    background-color: #000;
    color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .reduced-motion-safe {
    transform: none !important;
  }
}