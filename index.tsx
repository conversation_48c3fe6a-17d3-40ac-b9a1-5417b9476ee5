
import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import App from './App';
import { SupabaseBlogProvider } from './context/SupabaseBlogContext';

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
      <BrowserRouter>
        <SupabaseBlogProvider>
          <App />
        </SupabaseBlogProvider>
      </BrowserRouter>
  </React.StrictMode>
);
